/*
 * SPDX-License-Identifier: AGPL-3.0-only
 * project-filters.tsx
 * Copyright (C) 2025 Nextify Limited
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU Affero General Public License as
 * published by the Free Software Foundation, either version 3 of the
 * License, or (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the
 * GNU Affero General Public License for more details.
 *
 * You should have received a copy of the GNU Affero General Public License
 * along with this program. If not, see <https://www.gnu.org/licenses/>.
 *
 */

'use client'

import { useState, useMemo } from 'react'
import { Search, Filter, SortAsc, SortDesc, X, Grid3X3, List } from 'lucide-react'
import { Input } from '@libra/ui/components/input'
import { Button } from '@libra/ui/components/button'
import { Badge } from '@libra/ui/components/badge'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@libra/ui/components/select'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@libra/ui/components/dropdown-menu'
import { cn } from '@libra/ui/lib/utils'
import * as m from '@/paraglide/messages'
import type { Project } from '../section-cards'

export type FilterStatus = 'all' | 'active' | 'inactive'
export type SortBy = 'name' | 'created' | 'updated'
export type SortOrder = 'asc' | 'desc'
export type ViewMode = 'grid' | 'list'

interface ProjectFiltersProps {
  projects: Project[]
  onFilteredProjectsChange: (projects: Project[]) => void
  className?: string
}

interface FilterState {
  search: string
  status: FilterStatus
  sortBy: SortBy
  sortOrder: SortOrder
  viewMode: ViewMode
}

export function ProjectFilters({ projects, onFilteredProjectsChange, className }: ProjectFiltersProps) {
  const [filters, setFilters] = useState<FilterState>({
    search: '',
    status: 'all',
    sortBy: 'updated',
    sortOrder: 'desc',
    viewMode: 'grid'
  })

  // Filtered and sorted projects
  const filteredProjects = useMemo(() => {
    let result = [...projects]

    // Apply search filter
    if (filters.search.trim()) {
      const searchTerm = filters.search.toLowerCase().trim()
      result = result.filter(project => 
        project.name.toLowerCase().includes(searchTerm) ||
        (project.initialMessage && project.initialMessage.toLowerCase().includes(searchTerm))
      )
    }

    // Apply status filter
    if (filters.status !== 'all') {
      result = result.filter(project => {
        const isActive = project.isActive !== false
        return filters.status === 'active' ? isActive : !isActive
      })
    }

    // Apply sorting
    result.sort((a, b) => {
      let comparison = 0

      switch (filters.sortBy) {
        case 'name':
          comparison = a.name.localeCompare(b.name)
          break
        case 'created':
          comparison = new Date(a.createdAt || 0).getTime() - new Date(b.createdAt || 0).getTime()
          break
        case 'updated':
          comparison = new Date(a.updatedAt || 0).getTime() - new Date(b.updatedAt || 0).getTime()
          break
      }

      return filters.sortOrder === 'asc' ? comparison : -comparison
    })

    return result
  }, [projects, filters])

  // Update parent component when filtered projects change
  useMemo(() => {
    onFilteredProjectsChange(filteredProjects)
  }, [filteredProjects, onFilteredProjectsChange])

  const updateFilter = <K extends keyof FilterState>(key: K, value: FilterState[K]) => {
    setFilters(prev => ({ ...prev, [key]: value }))
  }

  const clearFilters = () => {
    setFilters({
      search: '',
      status: 'all',
      sortBy: 'updated',
      sortOrder: 'desc',
      viewMode: filters.viewMode // Keep view mode
    })
  }

  const hasActiveFilters = filters.search.trim() !== '' || filters.status !== 'all'

  return (
    <div className={cn('flex flex-col gap-4 sm:gap-6', className)}>
      {/* Search and Main Controls */}
      <div className='flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between'>
        {/* Search Input */}
        <div className='relative flex-1 max-w-md'>
          <Search className='absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground' />
          <Input
            placeholder='Search projects...'
            value={filters.search}
            onChange={(e) => updateFilter('search', e.target.value)}
            className='pl-10 pr-10'
          />
          {filters.search && (
            <Button
              variant="ghost"
              size="sm"
              onClick={() => updateFilter('search', '')}
              className='absolute right-1 top-1/2 transform -translate-y-1/2 h-6 w-6 p-0 hover:bg-muted'
            >
              <X className='h-3 w-3' />
            </Button>
          )}
        </div>

        {/* Filter Controls */}
        <div className='flex items-center gap-2'>
          {/* Status Filter */}
          <Select value={filters.status} onValueChange={(value: FilterStatus) => updateFilter('status', value)}>
            <SelectTrigger className='w-32'>
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Status</SelectItem>
              <SelectItem value="active">Active</SelectItem>
              <SelectItem value="inactive">Inactive</SelectItem>
            </SelectContent>
          </Select>

          {/* Sort Dropdown */}
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" size="sm" className='gap-2'>
                {filters.sortOrder === 'asc' ? (
                  <SortAsc className='h-4 w-4' />
                ) : (
                  <SortDesc className='h-4 w-4' />
                )}
                Sort
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuLabel>Sort by</DropdownMenuLabel>
              <DropdownMenuSeparator />
              <DropdownMenuItem onClick={() => updateFilter('sortBy', 'name')}>
                Name {filters.sortBy === 'name' && '✓'}
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => updateFilter('sortBy', 'updated')}>
                Last Updated {filters.sortBy === 'updated' && '✓'}
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => updateFilter('sortBy', 'created')}>
                Created {filters.sortBy === 'created' && '✓'}
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem onClick={() => updateFilter('sortOrder', filters.sortOrder === 'asc' ? 'desc' : 'asc')}>
                {filters.sortOrder === 'asc' ? 'Descending' : 'Ascending'}
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>

          {/* View Mode Toggle */}
          <div className='flex items-center border border-border rounded-md p-1'>
            <Button
              variant={filters.viewMode === 'grid' ? 'default' : 'ghost'}
              size="sm"
              onClick={() => updateFilter('viewMode', 'grid')}
              className='h-7 w-7 p-0'
            >
              <Grid3X3 className='h-3.5 w-3.5' />
            </Button>
            <Button
              variant={filters.viewMode === 'list' ? 'default' : 'ghost'}
              size="sm"
              onClick={() => updateFilter('viewMode', 'list')}
              className='h-7 w-7 p-0'
            >
              <List className='h-3.5 w-3.5' />
            </Button>
          </div>

          {/* Clear Filters */}
          {hasActiveFilters && (
            <Button variant="ghost" size="sm" onClick={clearFilters} className='gap-2'>
              <X className='h-4 w-4' />
              Clear
            </Button>
          )}
        </div>
      </div>

      {/* Active Filters Display */}
      {hasActiveFilters && (
        <div className='flex items-center gap-2 flex-wrap'>
          <span className='text-sm text-muted-foreground'>Active filters:</span>
          {filters.search && (
            <Badge variant="secondary" className='gap-1'>
              Search: "{filters.search}"
              <X 
                className='h-3 w-3 cursor-pointer hover:text-foreground' 
                onClick={() => updateFilter('search', '')}
              />
            </Badge>
          )}
          {filters.status !== 'all' && (
            <Badge variant="secondary" className='gap-1'>
              Status: {filters.status}
              <X 
                className='h-3 w-3 cursor-pointer hover:text-foreground' 
                onClick={() => updateFilter('status', 'all')}
              />
            </Badge>
          )}
        </div>
      )}

      {/* Results Summary */}
      <div className='flex items-center justify-between text-sm text-muted-foreground px-1'>
        <div className='flex items-center gap-2'>
          <span>
            {filteredProjects.length} of {projects.length} projects
            {hasActiveFilters && ' (filtered)'}
          </span>
        </div>
        <div className='flex items-center gap-2'>
          {filters.viewMode === 'grid' ? (
            <>
              <Grid3X3 className='h-3.5 w-3.5' />
              <span>Grid view</span>
            </>
          ) : (
            <>
              <List className='h-3.5 w-3.5' />
              <span>List view</span>
            </>
          )}
        </div>
      </div>
    </div>
  )
}