/*
 * SPDX-License-Identifier: AGPL-3.0-only
 * empty-state.tsx
 * Copyright (C) 2025 Nextify Limited
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU Affero General Public License as
 * published by the Free Software Foundation, either version 3 of the
 * License, or (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the
 * GNU Affero General Public License for more details.
 *
 * You should have received a copy of the GNU Affero General Public License
 * along with this program. If not, see <https://www.gnu.org/licenses/>.
 *
 */

'use client'

import { Button } from '@libra/ui/components/button'
import { Card } from '@libra/ui/components/card'
import { Plus, Sparkles, ArrowRight, Lightbulb } from 'lucide-react'
import { motion } from 'motion/react'
import * as m from '@/paraglide/messages'

export function EmptyState({ onCreateProject }: { onCreateProject: () => void }) {
  return (
    <div className='flex items-center justify-center py-16 px-4'>
      <Card className='max-w-2xl w-full p-8 sm:p-12 text-center border-0 bg-gradient-to-br from-muted/30 to-muted/10 relative overflow-hidden'>
        {/* Background decoration */}
        <div className='absolute inset-0 bg-grid-white/5 bg-grid-16 [mask-image:radial-gradient(ellipse_at_center,white,transparent)]' />
        
        {/* Floating elements */}
        <div className='absolute top-6 left-6 opacity-20'>
          <motion.div
            animate={{ 
              y: [0, -10, 0],
              rotate: [0, 5, 0]
            }}
            transition={{ 
              duration: 4,
              repeat: Number.POSITIVE_INFINITY,
              ease: "easeInOut"
            }}
          >
            <Lightbulb className='h-6 w-6 text-primary' />
          </motion.div>
        </div>
        
        <div className='absolute top-12 right-8 opacity-15'>
          <motion.div
            animate={{ 
              y: [0, 8, 0],
              rotate: [0, -5, 0]
            }}
            transition={{ 
              duration: 3,
              repeat: Number.POSITIVE_INFINITY,
              ease: "easeInOut",
              delay: 1
            }}
          >
            <Sparkles className='h-8 w-8 text-primary' />
          </motion.div>
        </div>

        <div className='relative z-10 space-y-6'>
          {/* Main icon */}
          <motion.div 
            className='flex justify-center'
            initial={{ scale: 0, rotate: -180 }}
            animate={{ scale: 1, rotate: 0 }}
            transition={{ 
              type: "spring",
              stiffness: 200,
              damping: 20,
              duration: 0.8
            }}
          >
            <div className='relative'>
              <div className='p-6 rounded-2xl bg-primary/10 border border-primary/20'>
                <Sparkles className='h-12 w-12 text-primary' />
              </div>
              {/* Glow effect */}
              <div className='absolute inset-0 rounded-2xl bg-primary/5 blur-xl scale-110 -z-10' />
            </div>
          </motion.div>

          {/* Title */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.3, duration: 0.6 }}
          >
            <h3 className='text-2xl sm:text-3xl font-bold text-foreground mb-2'>
              {m["dashboard.empty_state_title"]()}
            </h3>
            <p className='text-muted-foreground text-base sm:text-lg leading-relaxed max-w-md mx-auto'>
              {m["dashboard.empty_state_description"]()}
            </p>
          </motion.div>

          {/* Call to action */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.6, duration: 0.6 }}
            className='pt-2'
          >
            <Button
              size="lg"
              className='group bg-primary hover:bg-primary/90 text-primary-foreground shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105 px-8 py-3'
              onClick={onCreateProject}
            >
              <Plus className='h-5 w-5 mr-2 group-hover:rotate-90 transition-transform duration-300' />
              <span className='font-medium'>{m["dashboard.empty_state_button"]()}</span>
              <ArrowRight className='h-4 w-4 ml-2 group-hover:translate-x-1 transition-transform duration-300' />
            </Button>
          </motion.div>

          {/* Additional help text */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.9, duration: 0.6 }}
            className='pt-4'
          >
            <p className='text-xs text-muted-foreground/70'>
              Get started in seconds • No setup required • Free to use
            </p>
          </motion.div>
        </div>

        {/* Bottom accent */}
        <div className='absolute bottom-0 left-0 right-0 h-1 bg-gradient-to-r from-primary/0 via-primary/50 to-primary/0' />
      </Card>
    </div>
  )
} 