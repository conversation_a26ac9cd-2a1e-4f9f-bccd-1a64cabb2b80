/*
 * SPDX-License-Identifier: AGPL-3.0-only
 * skeleton-card.tsx
 * Copyright (C) 2025 Nextify Limited
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU Affero General Public License as
 * published by the Free Software Foundation, either version 3 of the
 * License, or (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the
 * GNU Affero General Public License for more details.
 *
 * You should have received a copy of the GNU Affero General Public License
 * along with this program. If not, see <https://www.gnu.org/licenses/>.
 *
 */

import { Skeleton } from '@libra/ui/components/skeleton'
import { Card, CardContent } from '@libra/ui/components/card'

export function SkeletonCard() {
  return (
    <Card className='border border-border/50 bg-card/50 overflow-hidden'>
      {/* Preview Image Skeleton */}
      <div className='relative h-40 bg-gradient-to-br from-muted/20 to-muted/40'>
        <Skeleton className='h-full w-full' />
        
        {/* Settings button placeholder */}
        <div className='absolute top-3 right-3'>
          <Skeleton className='h-8 w-8 rounded-md' />
        </div>
      </div>

      {/* Content Skeleton */}
      <CardContent className='p-4 space-y-3'>
        {/* Title and Description */}
        <div className='space-y-2'>
          <Skeleton className='h-5 w-3/4 rounded' />
          <div className='space-y-1'>
            <Skeleton className='h-4 w-full rounded' />
            <Skeleton className='h-4 w-5/6 rounded' />
          </div>
        </div>

        {/* Footer */}
        <div className='flex items-center justify-between pt-2 border-t border-border/50'>
          <div className='flex items-center gap-1.5'>
            <Skeleton className='h-3.5 w-3.5 rounded' />
            <Skeleton className='h-3 w-20 rounded' />
          </div>
          <Skeleton className='h-2 w-2 rounded-full' />
        </div>
      </CardContent>
    </Card>
  )
} 