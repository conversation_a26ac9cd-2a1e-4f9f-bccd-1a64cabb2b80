/*
 * SPDX-License-Identifier: AGPL-3.0-only
 * project-card.tsx
 * Copyright (C) 2025 Nextify Limited
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU Affero General Public License as
 * published by the Free Software Foundation, either version 3 of the
 * License, or (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the
 * GNU Affero General Public License for more details.
 *
 * You should have received a copy of the GNU Affero General Public License
 * along with this program. If not, see <https://www.gnu.org/licenses/>.
 *
 */

'use client'

import { memo, useState } from 'react'
import { formatDistanceToNow } from 'date-fns'
import { enUS, zhCN } from 'date-fns/locale'
import { Clock, MoreVertical, Folder, ImageIcon } from 'lucide-react'
import { motion } from 'motion/react'
import Image from 'next/image'
import Link from 'next/link'
import { useRouter } from 'next/navigation'
import { Card, CardContent } from '@libra/ui/components/card'
import { Badge } from '@libra/ui/components/badge'
import { Button } from '@libra/ui/components/button'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@libra/ui/components/dropdown-menu'
import { cn } from '@libra/ui/lib/utils'
import * as m from '@/paraglide/messages'
import { getLocale } from '@/paraglide/runtime'

// Static destructuring of message functions for better tree shaking
const {
  'dashboard.projectCard.time.updatedAgo': timeUpdatedAgo,
  'dashboard.projectCard.time.createdAgo': timeCreatedAgo,
  'dashboard.projectCard.time.justCreated': timeJustCreated,
  'dashboard.projectCard.noDescription': noDescription,
} = m

import { CreateProjectDialog } from '../create-project-dialog'
import { Loading } from '../loading'
import { ProjectDetailsDialog } from '../project-details-dialog'
import type { Project } from '../section-cards'

function ProjectCardComponent({ project }: { project: Project }) {
  const [isHovered, setIsHovered] = useState(false)
  const [isLoading, setIsLoading] = useState(false)
  const router = useRouter()
  const [showDetailsDialog, setShowDetailsDialog] = useState(false)
  const [showRequirementsDialog, setShowRequirementsDialog] = useState(false)

  // Check if project is inactive
  const isProjectInactive = project.isActive === false

  // Check if there's a valid preview image
  const hasValidPreviewImage =
    project.previewImageUrl &&
    project.previewImageUrl.trim() !== '' &&
    project.previewImageUrl !== 'null' &&
    project.previewImageUrl !== 'undefined'

  const handleProjectClick = (e: React.MouseEvent) => {
    e.preventDefault()

    // If project is inactive, prevent navigation
    if (isProjectInactive) {
      return
    }

    setIsLoading(true)

    if (!project.initialMessage) {
      setShowRequirementsDialog(true)
      setIsLoading(false)
    } else {
      router.push(`/project/${project.id}`)
    }
  }

  // Format time
  const formattedTime = () => {
    const currentLocale = getLocale()
    const dateLocale = currentLocale === 'zh' ? zhCN : enUS

    if (project.updatedAt) {
      const timeAgo = formatDistanceToNow(new Date(project.updatedAt), { locale: dateLocale })
      return timeUpdatedAgo({ time: timeAgo })
    }
    if (project.createdAt) {
      const timeAgo = formatDistanceToNow(new Date(project.createdAt), { locale: dateLocale })
      return timeCreatedAgo({ time: timeAgo })
    }
    return timeJustCreated()
  }

  return (
    <>
      <Card
        className={cn(
          'group relative overflow-hidden transition-all duration-300 cursor-pointer',
          'border border-border/50 bg-card/50 backdrop-blur-sm',
          'hover:border-primary/30 hover:shadow-lg hover:shadow-primary/5',
          isHovered && !isProjectInactive && 'scale-[1.02] shadow-xl shadow-primary/10',
          isProjectInactive && 'opacity-60 cursor-not-allowed bg-muted/30'
        )}
        onMouseEnter={() => setIsHovered(true)}
        onMouseLeave={() => setIsHovered(false)}
        onClick={handleProjectClick}
      >
        {/* Preview Image or Placeholder */}
        <div className='relative h-40 bg-gradient-to-br from-muted/20 to-muted/40 overflow-hidden'>
          {hasValidPreviewImage ? (
            <Image
              src={project.previewImageUrl || ''}
              alt={`${project.name} preview`}
              fill
              className={cn(
                'object-cover transition-all duration-500',
                'group-hover:scale-105',
                isProjectInactive && 'grayscale'
              )}
              sizes='(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw'
              loading='lazy'
            />
          ) : (
            <div className='flex items-center justify-center h-full'>
              <div className={cn(
                'p-4 rounded-full transition-colors',
                'bg-primary/10 text-primary/60',
                'group-hover:bg-primary/15 group-hover:text-primary/80'
              )}>
                <Folder className='h-8 w-8' />
              </div>
            </div>
          )}

          {/* Status Badge */}
          {isProjectInactive && (
            <div className='absolute top-3 left-3'>
              <Badge variant="secondary" className='text-xs bg-background/80 backdrop-blur-sm'>
                {m["dashboard.projectCard.inactive"]()}
              </Badge>
            </div>
          )}

          {/* Settings Dropdown */}
          <div className={cn(
            'absolute top-3 right-3 transition-opacity duration-200',
            isHovered && !isProjectInactive ? 'opacity-100' : 'opacity-0'
          )}>
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button
                  variant="ghost"
                  size="sm"
                  className='h-8 w-8 p-0 bg-background/80 backdrop-blur-sm hover:bg-background border border-border/50'
                  onClick={(e) => e.stopPropagation()}
                >
                  <MoreVertical className='h-4 w-4' />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuItem onClick={(e) => {
                  e.stopPropagation()
                  setShowDetailsDialog(true)
                }}>
                  {m["dashboard.projectCard.projectSettings"]()}
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>

          {/* Hover Overlay */}
          {isHovered && !isProjectInactive && (
            <motion.div
              className='absolute inset-0 bg-primary/5'
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              transition={{ duration: 0.2 }}
            />
          )}
        </div>

        {/* Content */}
        <CardContent className='p-4 space-y-3'>
          {/* Title */}
          <div className='space-y-2'>
            <h3 className={cn(
              'font-semibold text-base leading-tight line-clamp-1',
              'text-foreground group-hover:text-primary transition-colors'
            )}>
              {project.name}
            </h3>
            
            {/* Description */}
            <p className={cn(
              'text-sm text-muted-foreground line-clamp-2 leading-relaxed min-h-[2.5rem]'
            )}>
              {project.initialMessage || (
                <span className='italic text-muted-foreground/60'>
                  {noDescription()}
                </span>
              )}
            </p>
          </div>

          {/* Footer */}
          <div className='flex items-center justify-between pt-2 border-t border-border/50'>
            <div className='flex items-center gap-1.5 text-xs text-muted-foreground'>
              <Clock className='h-3.5 w-3.5' />
              <span className='truncate'>{formattedTime()}</span>
            </div>
            
            {/* Project Status Indicator */}
            <div className={cn(
              'h-2 w-2 rounded-full transition-colors',
              isProjectInactive ? 'bg-muted-foreground/40' : 'bg-green-500'
            )} />
          </div>
        </CardContent>

        {/* Loading Overlay */}
        {isLoading && (
          <motion.div
            className='absolute inset-0 bg-background/80 backdrop-blur-sm flex items-center justify-center z-50'
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.2 }}
          >
            <Loading className='w-8 h-8' />
          </motion.div>
        )}

        {/* Inactive Overlay */}
        {isProjectInactive && (
          <div className='absolute inset-0 bg-muted/20 backdrop-blur-[1px] z-10' />
        )}
      </Card>

      {/* Dialogs */}
      <ProjectDetailsDialog
        open={showDetailsDialog}
        onOpenChange={setShowDetailsDialog}
        projectId={project.id}
        projectName={project.name}
      />

      {showRequirementsDialog && (
        <CreateProjectDialog
          open={showRequirementsDialog}
          onOpenChange={setShowRequirementsDialog}
          preSelectedProjectId={project.id}
          preSelectedProjectName={project.name}
          skipFirstDialog={true}
        />
      )}
    </>
  )
}

export const ProjectCard = memo(ProjectCardComponent)
