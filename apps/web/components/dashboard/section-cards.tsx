/*
 * SPDX-License-Identifier: AGPL-3.0-only
 * section-cards.tsx
 * Copyright (C) 2025 Nextify Limited
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU Affero General Public License as
 * published by the Free Software Foundation, either version 3 of the
 * License, or (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the
 * GNU Affero General Public License for more details.
 *
 * You should have received a copy of the GNU Affero General Public License
 * along with this program. If not, see <https://www.gnu.org/licenses/>.
 *
 */

'use client'

import * as m from '@/paraglide/messages'
import { useTRPC } from '@/trpc/client'
import { useQuery } from '@tanstack/react-query'
import { Sparkles, Grid3X3 } from 'lucide-react'
import { useState } from 'react'
import { CreateProjectDialog } from './create-project-dialog'
import { EmptyState } from './ui/empty-state'
import { ProjectCard } from './ui/project-card'
import { ProjectCreateButton } from './ui/project-create-button'
import { SkeletonCard } from './ui/skeleton-card'
import { cn } from '@libra/ui/lib/utils'

// Project interface definition, exported for use by other components
export interface Project {
  id: string
  name: string
  initialMessage?: string | null
  updatedAt?: any
  createdAt?: any
  isActive?: boolean
  previewImageUrl?: string | null
}

export function SectionCards() {
  const [isCreateProjectOpen, setIsCreateProjectOpen] = useState(false)
  const trpc = useTRPC()
  const { data: projects, isLoading } = useQuery({
    ...trpc.project.list.queryOptions({}),
  })

  const projectCount = Array.isArray(projects) ? projects.length : 0
  const hasProjects = projectCount > 0

  return (
    <div className='flex flex-col space-y-6'>
      {/* Header Section */}
      <div className='flex items-center justify-between px-1'>
        <div className='flex items-center gap-3'>
          <div className='flex items-center gap-2'>
            <div className='p-2 rounded-lg bg-primary/10'>
              <Sparkles className='h-5 w-5 text-primary' />
            </div>
            <div>
              <h2 className='text-xl font-semibold tracking-tight'>
                {m['dashboard.my_projects']()}
              </h2>
              {hasProjects && (
                <p className='text-sm text-muted-foreground mt-0.5'>
                  {projectCount} {projectCount === 1 ? 'project' : 'projects'}
                </p>
              )}
            </div>
          </div>
        </div>
        <ProjectCreateButton onClick={() => setIsCreateProjectOpen(true)} />
      </div>

      {/* Project Grid */}
      <div className='space-y-4'>
        {/* Grid Layout Info (only show when there are projects) */}
        {hasProjects && !isLoading && (
          <div className='flex items-center gap-2 text-xs text-muted-foreground px-1'>
            <Grid3X3 className='h-3.5 w-3.5' />
            <span>Grid view</span>
          </div>
        )}

        {/* Projects Grid */}
        <div className={cn(
          'grid gap-4 sm:gap-5 lg:gap-6',
          // Responsive grid columns with better breakpoints
          'grid-cols-1',                    // Mobile: 1 column
          'sm:grid-cols-2',                 // Small screens: 2 columns  
          'lg:grid-cols-3',                 // Large screens: 3 columns
          'xl:grid-cols-4',                 // Extra large: 4 columns
          '2xl:grid-cols-5',                // 2XL screens: 5 columns
          // Minimum card width constraints
          'auto-rows-fr',                   // Equal height rows
          '[&>*]:min-h-[280px]'            // Minimum card height
        )}>
          {isLoading ? (
            // Loading state with proper count based on screen size
            Array.from({ length: 8 }, () => (
              <SkeletonCard key={`skeleton-${Math.random()}`} />
            ))
          ) : hasProjects ? (
            projects.map((project) => (
              <ProjectCard key={project.id} project={project} />
            ))
          ) : (
            <div className='col-span-full'>
              <EmptyState onCreateProject={() => setIsCreateProjectOpen(true)} />
            </div>
          )}
        </div>

        {/* Grid Stats Footer (only show when there are projects) */}
        {hasProjects && !isLoading && (
          <div className='flex items-center justify-center pt-4 border-t border-border/50'>
            <p className='text-xs text-muted-foreground'>
              Showing {projectCount} of {projectCount} projects
            </p>
          </div>
        )}
      </div>

      <CreateProjectDialog 
        open={isCreateProjectOpen} 
        onOpenChange={setIsCreateProjectOpen} 
      />
    </div>
  )
}
