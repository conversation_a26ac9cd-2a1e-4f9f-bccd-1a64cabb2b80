/*
 * SPDX-License-Identifier: AGPL-3.0-only
 * section-cards.tsx
 * Copyright (C) 2025 Nextify Limited
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU Affero General Public License as
 * published by the Free Software Foundation, either version 3 of the
 * License, or (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the
 * GNU Affero General Public License for more details.
 *
 * You should have received a copy of the GNU Affero General Public License
 * along with this program. If not, see <https://www.gnu.org/licenses/>.
 *
 */

'use client'

import * as m from '@/paraglide/messages'
import { useTRPC } from '@/trpc/client'
import { useQuery } from '@tanstack/react-query'
import { Sparkles } from 'lucide-react'
import { useState, useCallback } from 'react'
import { CreateProjectDialog } from './create-project-dialog'
import { EmptyState } from './ui/empty-state'
import { ProjectCard } from './ui/project-card'
import { ProjectCreateButton } from './ui/project-create-button'
import { SkeletonCard } from './ui/skeleton-card'
import { ProjectFilters } from './ui/project-filters'
import { cn } from '@libra/ui/lib/utils'

// Project interface definition, exported for use by other components
export interface Project {
  id: string
  name: string
  initialMessage?: string | null
  updatedAt?: any
  createdAt?: any
  isActive?: boolean
  previewImageUrl?: string | null
}

export function SectionCards() {
  const [isCreateProjectOpen, setIsCreateProjectOpen] = useState(false)
  const [filteredProjects, setFilteredProjects] = useState<Project[]>([])
  
  const trpc = useTRPC()
  const { data: projects, isLoading } = useQuery({
    ...trpc.project.list.queryOptions({}),
  })

  const allProjects = Array.isArray(projects) ? projects : []
  const hasProjects = allProjects.length > 0
  const hasFilteredProjects = filteredProjects.length > 0

  const handleFilteredProjectsChange = useCallback((filtered: Project[]) => {
    setFilteredProjects(filtered)
  }, [])

  return (
    <div className='flex flex-col space-y-6'>
      {/* Header Section */}
      <div className='flex items-center justify-between px-1'>
        <div className='flex items-center gap-3'>
          <div className='flex items-center gap-2'>
            <div className='p-2 rounded-lg bg-primary/10'>
              <Sparkles className='h-5 w-5 text-primary' />
            </div>
            <div>
              <h2 className='text-xl font-semibold tracking-tight'>
                {m['dashboard.my_projects']()}
              </h2>
              {hasProjects && (
                <p className='text-sm text-muted-foreground mt-0.5'>
                  {allProjects.length} {allProjects.length === 1 ? 'project' : 'projects'}
                </p>
              )}
            </div>
          </div>
        </div>
        <ProjectCreateButton onClick={() => setIsCreateProjectOpen(true)} />
      </div>

      {/* Project Filters */}
      {hasProjects && !isLoading && (
        <ProjectFilters
          projects={allProjects}
          onFilteredProjectsChange={handleFilteredProjectsChange}
        />
      )}

      {/* Projects Grid */}
      <div className='space-y-4'>
        <div className={cn(
          'grid gap-4 sm:gap-5 lg:gap-6',
          // Responsive grid columns with better breakpoints
          'grid-cols-1',                    // Mobile: 1 column
          'sm:grid-cols-2',                 // Small screens: 2 columns  
          'lg:grid-cols-3',                 // Large screens: 3 columns
          'xl:grid-cols-4',                 // Extra large: 4 columns
          '2xl:grid-cols-5',                // 2XL screens: 5 columns
          // Minimum card width constraints
          'auto-rows-fr',                   // Equal height rows
          '[&>*]:min-h-[280px]'            // Minimum card height
        )}>
          {isLoading ? (
            // Loading state with proper count based on screen size
            Array.from({ length: 8 }, () => (
              <SkeletonCard key={`skeleton-${Math.random()}`} />
            ))
          ) : hasProjects ? (
            hasFilteredProjects ? (
              filteredProjects.map((project) => (
                <ProjectCard key={project.id} project={project} />
              ))
            ) : (
              // No results found after filtering
              <div className='col-span-full flex items-center justify-center py-16'>
                <div className='text-center space-y-2'>
                  <div className='p-4 rounded-full bg-muted/20 w-fit mx-auto mb-4'>
                    <Sparkles className='h-8 w-8 text-muted-foreground/50' />
                  </div>
                  <h3 className='text-lg font-medium text-muted-foreground'>
                    No projects found
                  </h3>
                  <p className='text-sm text-muted-foreground/70 max-w-sm'>
                    Try adjusting your search or filter criteria to find what you're looking for.
                  </p>
                </div>
              </div>
            )
          ) : (
            <div className='col-span-full'>
              <EmptyState onCreateProject={() => setIsCreateProjectOpen(true)} />
            </div>
          )}
        </div>
      </div>

      <CreateProjectDialog 
        open={isCreateProjectOpen} 
        onOpenChange={setIsCreateProjectOpen} 
      />
    </div>
  )
}
